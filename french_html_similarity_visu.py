# https://github.com/networkx/networkx
import networkx as nx

from pyvis.network import Network
import json


def visualize_graph_pyvis(graphe, output_filename="interactive_graph.html"):
    """
    Visualise le graphe de similarité de manière interactive avec Pyvis.

    Args:
        G (nx.Graph): Le graphe NetworkX à visualiser.
        output_filename (str): Le nom du fichier HTML de sortie.
    """
    nt = Network(
        height="800px",
        width="100%",
        directed=nx.is_directed(graphe),
    )

    nt.from_nx(graphe)

    edges = graphe.edges()
    weights = [graphe[u][v].get("width", 1.0) for u, v in edges]

    max_weight = max(weights)
    min_weight = min(weights)
    weight_range = max_weight - min_weight if max_weight != min_weight else 1

    for edge in nt.edges:
        original_weight = graphe[edge["from"]][edge["to"]].get("width", 1.0)

        # Normaliser l'épaisseur pour une meilleure visualisation
        # L'attribut 'width' contrôle l'épaisseur visuelle
        if weights:
            normalized_width = (original_weight - min_weight) / weight_range * 8 + 1
            edge["width"] = normalized_width
        else:
            edge["width"] = 1

        # L'attribut 'title' affiche l'info au survol de la souris
        edge["title"] = f"Similarité : {original_weight:.3f}"
        edge["color"] = "gray"

    for node in nt.nodes:
        node["size"] = 20  # Taille fixe pour les noeuds
        node["color"] = "lightblue"

        full_url = node["id"]
        short_url = full_url.split("/")[-1] if "/" in full_url else full_url
        if len(short_url) > 20:
            short_url = short_url[:17] + "..."
        node["label"] = short_url

        # Le titre est le tooltip affiché au survol (on met l'URL complète ici)
        node["title"] = full_url

    # 4. Ajouter la configuration de la physique pour une meilleure interactivité
    # Cette configuration utilise le solveur "barnesHut" qui est efficace pour les graphes
    # de taille moyenne. Il évite que les noeuds se superposent.
    physics_options = {
        "barnesHut": {
            "theta": 0.5,
            "gravitationalConstant": -8000,
            "centralGravity": 0.3,
            "springLength": 95,
            "springConstant": 0.04,
            "damping": 0.09,
            "avoidOverlap": 0.1,
        },
        "minVelocity": 0.75,
    }

    options_str = json.dumps(physics_options)
    #nt.set_options(f"var options = {options_str}")
    nt.set_options("""
        var options = {
          "physics": {
            "enabled": true,
            "stabilization": {"enabled": true, "iterations": 100},
            "barnesHut": {
              "gravitationalConstant": -8000,
              "centralGravity": 0.3,
              "springLength": 95,
              "springConstant": 0.04,
              "damping": 0.09,
            }
          },
          "nodes": {
            "font": {"size": 12}
          },
          "edges": {
            "font": {"size": 10}
          },
          "interaction": {
            "hover": true,
            "tooltipDelay": 200,
            "dragNodes": true,
            "dragView": true,
            "zoomView": true
          }
        }
    """)

    # Alternative: ajouter un panneau de contrôle pour que l'utilisateur ajuste la physique
    try:
        nt.show_buttons()
    except AttributeError:
        # Fallback for pyvis compatibility issue
        print("Note: Control buttons not available due to pyvis version compatibility")

    nt.save_graph(output_filename)
    print(f"Le graphe interactif a été sauvegardé dans '{output_filename}'")


def main():
    sim_graph = nx.read_gexf("urls_similarity_graph.gexf")
    visualize_graph_pyvis(sim_graph)

if __name__ == "__main__":
    main()
